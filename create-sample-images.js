// Simple script to create placeholder images for testing
const fs = require('fs');
const path = require('path');

// Create directories
const dirs = [
  'public/designportfolio/portfolio/logos',
  'public/designportfolio/portfolio/posters',
  'public/designportfolio/portfolio/drawing',
  'public/designportfolio/portfolio/layouts'
];

dirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Create simple SVG placeholder images
const createSVG = (text, color) => `
<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="${color}"/>
  <text x="50%" y="50%" font-family="Arial" font-size="24" fill="white" text-anchor="middle" dy=".3em">${text}</text>
</svg>
`;

// Sample data
const samples = [
  { folder: 'logos', items: ['Logo 1', 'Logo 2', 'Logo 3'], color: '#3B82F6' },
  { folder: 'posters', items: ['Poster 1', 'Poster 2'], color: '#EF4444' },
  { folder: 'drawing', items: ['Art 1', 'Art 2'], color: '#10B981' },
  { folder: 'layouts', items: ['Layout 1'], color: '#8B5CF6' }
];

samples.forEach(({ folder, items, color }) => {
  items.forEach((item, index) => {
    const svg = createSVG(item, color);
    const filename = `${folder}_${index + 1}.svg`;
    const filepath = path.join('public/designportfolio/portfolio', folder, filename);
    fs.writeFileSync(filepath, svg);
    console.log(`Created: ${filepath}`);
  });
});

console.log('Sample images created successfully!');
