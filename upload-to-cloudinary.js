const cloudinary = require('cloudinary').v2;
const fs = require('fs');
const path = require('path');

// Configure Cloudinary with your credentials
cloudinary.config({
  cloud_name: 'c-22ffb831b854e3d24daa4c048310f1',
  api_key: 'your_api_key_here', // You'll need to get this from your dashboard
  api_secret: 'your_api_secret_here' // You'll need to get this from your dashboard
});

// Function to upload a single file
async function uploadFile(filePath, folder = 'portfolio') {
  try {
    const result = await cloudinary.uploader.upload(filePath, {
      folder: folder,
      resource_type: 'auto', // Automatically detect if it's image or video
      use_filename: true,
      unique_filename: false,
    });
    
    console.log(`✅ Uploaded: ${path.basename(filePath)} -> ${result.secure_url}`);
    return result;
  } catch (error) {
    console.error(`❌ Failed to upload ${filePath}:`, error.message);
    return null;
  }
}

// Function to upload all files in a directory
async function uploadDirectory(dirPath, cloudinaryFolder) {
  const files = fs.readdirSync(dirPath, { withFileTypes: true });
  const results = [];
  
  for (const file of files) {
    const fullPath = path.join(dirPath, file.name);
    
    if (file.isDirectory()) {
      // Recursively upload subdirectories
      const subResults = await uploadDirectory(fullPath, `${cloudinaryFolder}/${file.name}`);
      results.push(...subResults);
    } else if (file.isFile()) {
      // Upload individual files
      const result = await uploadFile(fullPath, cloudinaryFolder);
      if (result) {
        results.push({
          originalPath: fullPath,
          cloudinaryUrl: result.secure_url,
          publicId: result.public_id,
          fileName: file.name
        });
      }
    }
  }
  
  return results;
}

// Main upload function
async function uploadPortfolioFiles() {
  console.log('🚀 Starting portfolio upload to Cloudinary...\n');
  
  const uploadResults = [];
  
  // Upload videos
  if (fs.existsSync('temp_large_files/videos')) {
    console.log('📹 Uploading videos...');
    const videoResults = await uploadDirectory('temp_large_files/videos', 'portfolio/videos');
    uploadResults.push(...videoResults);
  }
  
  // Upload logos with videos
  if (fs.existsSync('public/designportfolio/portfolio/logos')) {
    console.log('🎨 Uploading logos...');
    const logoResults = await uploadDirectory('public/designportfolio/portfolio/logos', 'portfolio/logos');
    uploadResults.push(...logoResults);
  }
  
  // Save results to a JSON file for easy reference
  fs.writeFileSync('cloudinary-urls.json', JSON.stringify(uploadResults, null, 2));
  
  console.log(`\n✅ Upload complete! ${uploadResults.length} files uploaded.`);
  console.log('📄 URLs saved to cloudinary-urls.json');
  
  return uploadResults;
}

// Run the upload
if (require.main === module) {
  uploadPortfolioFiles().catch(console.error);
}

module.exports = { uploadPortfolioFiles, uploadFile };
