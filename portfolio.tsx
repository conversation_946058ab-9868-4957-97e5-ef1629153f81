"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Mail,
  Linkedin,
  Palette,
  Download,
  Send,
  ExternalLink,
  Play,
  ImageIcon,
  FileText,
  Brush,
  Layout,
  Zap,
} from "lucide-react"
import Image from "next/image"

export default function Portfolio() {
  const [activeTab, setActiveTab] = useState("videos")
  const [hoveredProject, setHoveredProject] = useState<number | null>(null)
  const [selectedProject, setSelectedProject] = useState<any>(null)

  const tabs = [
    { id: "videos", label: "Video Editing", icon: Play, count: "40+" },
    { id: "logos", label: "Logo Design", icon: Zap, count: "12+" },
    { id: "posters", label: "Poster Design", icon: ImageIcon, count: "25+" },
    { id: "drawing", label: "Digital Art", icon: Brush, count: "5+" },
    { id: "layouts", label: "Layout Design", icon: Layout, count: "2+" },
  ]

  // Your actual portfolio content
  const portfolioContent = {
    videos: [
      {
        id: 1,
        title: "SR Reality Showcase",
        description: "Real estate promotional video with dynamic visuals.",
        media: "/designportfolio/portfolio/videos/SRreality.mp4",
        type: "Video",
        isVideo: true,
      },
      {
        id: 2,
        title: "SpaceX Tribute",
        description: "Space exploration themed video with cinematic effects.",
        media: "/designportfolio/portfolio/videos/spaceX.mp4",
        type: "Video",
        isVideo: true,
      },
      {
        id: 3,
        title: "EarPods Pro Commercial",
        description: "Product showcase video with sleek presentation.",
        media: "/designportfolio/portfolio/videos/earpods pro.mp4",
        type: "Video",
        isVideo: true,
      },
      {
        id: 4,
        title: "Independence Day Celebration V1",
        description: "Patriotic video content for national celebration.",
        media: "/designportfolio/portfolio/videos/independence day(1).mp4",
        type: "Video",
        isVideo: true,
      },
      {
        id: 5,
        title: "Independence Day Celebration V2",
        description: "Second version of patriotic celebration content.",
        media: "/designportfolio/portfolio/videos/independence day(2).mp4",
        type: "Video",
        isVideo: true,
      },
      {
        id: 6,
        title: "Independence Reel",
        description: "Short-form patriotic content for social media.",
        media: "/designportfolio/portfolio/videos/independence reel.mp4",
        type: "Video",
        isVideo: true,
      },
      // Additional videos will be added later
      // {
      //   id: 7,
      //   title: "SCS Intro Video",
      //   description: "Professional company introduction video with smooth transitions.",
      //   media: "/designportfolio/portfolio/videos/SCSINTRO1 (2).mp4",
      //   type: "Video",
      //   isVideo: true,
      // },
      // {
      //   id: 8,
      //   title: "Cinema Promo",
      //   description: "Movie theater promotional content with dramatic flair.",
      //   media: "/designportfolio/portfolio/videos/cinema.mp4",
      //   type: "Video",
      //   isVideo: true,
      // },
    ],
    logos: [
      {
        id: 1,
        title: "Birbal Suredia",
        description: "Professional brand identity design with elegant typography.",
        media: "/designportfolio/portfolio/logos/Birbalsuredia.png",
        type: "Logo",
        isVideo: false,
      },
      {
        id: 2,
        title: "FOODIE Brand",
        description: "Food industry logo with appetizing visual appeal.",
        media: "/designportfolio/portfolio/logos/FOODIE.jpeg",
        type: "Logo",
        isVideo: false,
      },
      {
        id: 3,
        title: "HIREDIN",
        description: "Professional recruitment platform logo design.",
        media: "/designportfolio/portfolio/logos/HIREDIN.png",
        type: "Logo",
        isVideo: false,
      },
      {
        id: 4,
        title: "MARKETMIND",
        description: "Marketing agency logo with strategic visual elements.",
        media: "/designportfolio/portfolio/logos/MARKETMIND.webp",
        type: "Logo",
        isVideo: false,
      },
      {
        id: 5,
        title: "RAM Logo",
        description: "Bold and memorable brand identity design.",
        media: "/designportfolio/portfolio/logos/RAM Logo.jpeg",
        type: "Logo",
        isVideo: false,
      },
      {
        id: 6,
        title: "A2C Brand",
        description: "Modern minimalist logo with clean aesthetics.",
        media: "/designportfolio/portfolio/logos/a2c.png",
        type: "Logo",
        isVideo: false,
      },
      {
        id: 7,
        title: "Calculator App",
        description: "Tech app logo with functional design elements.",
        media: "/designportfolio/portfolio/logos/calci.png",
        type: "Logo",
        isVideo: false,
      },
      {
        id: 8,
        title: "The Akatsuki",
        description: "Anime-inspired logo design with dynamic styling.",
        media: "/designportfolio/portfolio/logos/the akatsuki.jpeg",
        type: "Logo",
        isVideo: false,
      },
      {
        id: 9,
        title: "Timble Brand",
        description: "Creative brand identity with unique visual approach.",
        media: "/designportfolio/portfolio/logos/timble..png",
        type: "Logo",
        isVideo: false,
      },
      {
        id: 10,
        title: "RUN Animation",
        description: "Animated logo with dynamic motion graphics.",
        media: "/designportfolio/portfolio/logos/RUN (1).mp4",
        type: "Logo",
        isVideo: true,
      },
      {
        id: 11,
        title: "CoderX Animation",
        description: "Tech brand animated logo with coding theme.",
        media: "/designportfolio/portfolio/logos/coderX.mp4",
        type: "Logo",
        isVideo: true,
      },
      {
        id: 12,
        title: "XYPO Animation",
        description: "Modern animated brand identity with smooth transitions.",
        media: "/designportfolio/portfolio/logos/xypo.mp4",
        type: "Logo",
        isVideo: true,
      },
    ],
    posters: [
      {
        id: 1,
        title: "Blinkit Campaign",
        description: "Modern delivery service promotional poster design.",
        media: "/designportfolio/portfolio/posters/Blinkit.png",
        type: "Poster",
        isVideo: false,
      },
      {
        id: 2,
        title: "College Seminar",
        description: "Educational event poster with professional layout.",
        media: "/designportfolio/portfolio/posters/Poster for college seminar.png",
        type: "Poster",
        isVideo: false,
      },
      {
        id: 3,
        title: "RPS Tournament",
        description: "Gaming tournament poster with dynamic design.",
        media: "/designportfolio/portfolio/posters/RPS (1).png",
        type: "Poster",
        isVideo: false,
      },
      {
        id: 4,
        title: "SARVOTTAM Event",
        description: "Cultural event poster with traditional elements.",
        media: "/designportfolio/portfolio/posters/SARVOTTAM 1 (1).png",
        type: "Poster",
        isVideo: false,
      },
      {
        id: 5,
        title: "Admission Campaign",
        description: "Educational institution promotional poster.",
        media: "/designportfolio/portfolio/posters/admission poster (1).png",
        type: "Poster",
        isVideo: false,
      },
      {
        id: 6,
        title: "Dance & Music Event",
        description: "Vibrant poster for cultural performance event.",
        media: "/designportfolio/portfolio/posters/dance & music.png",
        type: "Poster",
        isVideo: false,
      },
      {
        id: 7,
        title: "Fashion Show",
        description: "Elegant poster design for fashion event.",
        media: "/designportfolio/portfolio/posters/fashion show.png",
        type: "Poster",
        isVideo: false,
      },
      {
        id: 8,
        title: "Guru Nanak Jayanti",
        description: "Religious celebration poster with spiritual design.",
        media: "/designportfolio/portfolio/posters/guru nanak jayanti.png",
        type: "Poster",
        isVideo: false,
      },
      {
        id: 9,
        title: "RPS Series 1",
        description: "Gaming tournament series promotional design.",
        media: "/designportfolio/portfolio/posters/rps 1.png",
        type: "Poster",
        isVideo: false,
      },
      {
        id: 10,
        title: "RPS Series 2",
        description: "Continuation of gaming tournament poster series.",
        media: "/designportfolio/portfolio/posters/rps 2.png",
        type: "Poster",
        isVideo: false,
      },
      {
        id: 11,
        title: "Singing Competition",
        description: "Music competition poster with artistic flair.",
        media: "/designportfolio/portfolio/posters/singing.jpg",
        type: "Poster",
        isVideo: false,
      },
      {
        id: 12,
        title: "Animated Poster",
        description: "Dynamic animated poster with motion graphics.",
        media: "/designportfolio/portfolio/posters/Untitled design (1).mp4",
        type: "Poster",
        isVideo: true,
      },
    ],
    drawing: [
      {
        id: 1,
        title: "Artistic Sketch 1",
        description: "Hand-drawn artwork showcasing traditional sketching skills.",
        media: "/designportfolio/portfolio/drawing/IMG_20230620_134005.jpg",
        type: "Drawing",
        isVideo: false,
      },
      {
        id: 2,
        title: "Artistic Sketch 2",
        description: "Detailed pencil work with intricate shading techniques.",
        media: "/designportfolio/portfolio/drawing/IMG_20230620_134023.jpg",
        type: "Drawing",
        isVideo: false,
      },
      {
        id: 3,
        title: "Artistic Sketch 3",
        description: "Creative illustration demonstrating artistic vision.",
        media: "/designportfolio/portfolio/drawing/IMG_20230620_134032.jpg",
        type: "Drawing",
        isVideo: false,
      },
      {
        id: 4,
        title: "Artistic Sketch 4",
        description: "Expressive drawing with dynamic composition.",
        media: "/designportfolio/portfolio/drawing/IMG_20230620_134042.jpg",
        type: "Drawing",
        isVideo: false,
      },
      {
        id: 5,
        title: "Artistic Sketch 5",
        description: "Masterful pencil work showcasing technical skill.",
        media: "/designportfolio/portfolio/drawing/IMG_20230620_134106.jpg",
        type: "Drawing",
        isVideo: false,
      },
    ],
    layouts: [
      {
        id: 1,
        title: "Seth Company Layout",
        description: "Professional business layout design with clean typography.",
        media: "/designportfolio/portfolio/layouts/<EMAIL>",
        type: "Layout",
        isVideo: false,
      },
      {
        id: 2,
        title: "Post Layout Design",
        description: "Social media post layout with engaging visual hierarchy.",
        media: "/designportfolio/portfolio/layouts/post layout.png",
        type: "Layout",
        isVideo: false,
      },
    ],
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white overflow-x-hidden">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-3/4 left-1/2 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Header */}
      <header className="relative z-10 w-full py-8 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-cyan-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent mb-2">
            Manasvi
          </h1>
          <div className="relative inline-block">
            <p className="text-xl md:text-2xl text-gray-300 font-light tracking-wide">
              Video Editor & Graphic Designer
            </p>
            <div className="absolute -bottom-2 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-cyan-400 to-transparent animate-pulse"></div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative z-10 py-20 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <p className="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed">
            Creating stunning visuals and compelling video content that tells your story.
          </p>
          <Button className="group relative overflow-hidden bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-400 hover:to-purple-400 text-white font-semibold py-3 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-cyan-500/25">
            <span className="relative z-10 flex items-center gap-2">
              <Download className="w-5 h-5" />
              View My Reel
            </span>
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-cyan-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </Button>
        </div>
      </section>

      {/* Portfolio Section with Tabs */}
      <section className="relative z-10 py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold text-center mb-16 bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
            My Work
          </h2>

          {/* Tab Navigation */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {tabs.map((tab) => {
              const IconComponent = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`group relative flex items-center gap-2 px-6 py-3 rounded-full transition-all duration-300 transform hover:scale-105 ${
                    activeTab === tab.id
                      ? "bg-gradient-to-r from-cyan-500 to-purple-500 text-white shadow-lg shadow-cyan-500/25"
                      : "bg-white/5 border border-white/10 text-gray-300 hover:border-cyan-400/50 hover:text-cyan-400"
                  }`}
                >
                  <IconComponent className="w-4 h-4" />
                  <span className="font-medium">{tab.label}</span>
                  <span className="text-xs opacity-75">({tab.count})</span>
                  {activeTab !== tab.id && (
                    <div className="absolute inset-0 rounded-full bg-gradient-to-r from-cyan-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  )}
                </button>
              )
            })}
          </div>

          {/* Tab Content */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {portfolioContent[activeTab as keyof typeof portfolioContent]?.map((project) => (
              <div
                key={project.id}
                className="group relative overflow-hidden rounded-2xl backdrop-blur-md bg-white/5 border border-white/10 hover:border-cyan-400/50 transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-cyan-500/20 cursor-pointer"
                onMouseEnter={() => setHoveredProject(project.id)}
                onMouseLeave={() => setHoveredProject(null)}
                onClick={() => setSelectedProject(project)}
              >
                <div className="relative overflow-hidden">
                  {(project as any).isVideo ? (
                    <video
                      src={(project as any).media}
                      className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
                      muted
                      loop
                      playsInline
                      onMouseEnter={(e) => e.currentTarget.play()}
                      onMouseLeave={(e) => e.currentTarget.pause()}
                    />
                  ) : (
                    <Image
                      src={(project as any).media || "/placeholder.svg"}
                      alt={project.title}
                      width={400}
                      height={300}
                      className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
                    />
                  )}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  {(project as any).isVideo && (
                    <div className="absolute top-4 left-4 bg-black/50 backdrop-blur-sm text-white text-xs py-1 px-2 rounded-full flex items-center gap-1">
                      <Play className="w-3 h-3" />
                      Video
                    </div>
                  )}
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-2 text-white group-hover:text-cyan-400 transition-colors duration-300">
                    {project.title}
                  </h3>
                  <p className="text-gray-400 text-sm leading-relaxed">{project.description}</p>
                </div>
                {hoveredProject === project.id && (
                  <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-cyan-500/20 backdrop-blur-sm rounded-full p-2 border border-cyan-400/30">
                      <ExternalLink className="w-4 h-4 text-cyan-400" />
                    </div>
                  </div>
                )}
                <div className="absolute bottom-4 left-4 bg-black/50 backdrop-blur-sm text-white text-xs py-1 px-2 rounded-full">
                  {project.type}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="relative z-10 py-20 px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold text-center mb-16 bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
            Let's Connect
          </h2>

          {/* Contact Form */}
          <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-8 mb-12">
            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300">Name</label>
                  <Input
                    className="bg-white/5 border-white/20 text-white placeholder:text-gray-400 focus:border-cyan-400 focus:ring-cyan-400/20 rounded-lg"
                    placeholder="Your name"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300">Email</label>
                  <Input
                    type="email"
                    className="bg-white/5 border-white/20 text-white placeholder:text-gray-400 focus:border-cyan-400 focus:ring-cyan-400/20 rounded-lg"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Message</label>
                <Textarea
                  className="bg-white/5 border-white/20 text-white placeholder:text-gray-400 focus:border-cyan-400 focus:ring-cyan-400/20 rounded-lg min-h-[120px]"
                  placeholder="Tell me about your video editing or graphic design project..."
                />
              </div>
              <Button className="w-full group relative overflow-hidden bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-400 hover:to-purple-400 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-cyan-500/25">
                <span className="relative z-10 flex items-center justify-center gap-2">
                  <Send className="w-5 h-5" />
                  Send Message
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-cyan-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </Button>
            </form>
          </div>

          {/* Social Links */}
          <div className="flex justify-center space-x-8">
            <a
              href="#"
              className="group relative p-4 rounded-full bg-white/5 border border-white/10 hover:border-cyan-400/50 transition-all duration-300 transform hover:scale-110 hover:shadow-lg hover:shadow-cyan-500/20"
            >
              <Linkedin className="w-6 h-6 text-gray-400 group-hover:text-cyan-400 transition-colors duration-300" />
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-cyan-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </a>
            <a
              href="#"
              className="group relative p-4 rounded-full bg-white/5 border border-white/10 hover:border-purple-400/50 transition-all duration-300 transform hover:scale-110 hover:shadow-lg hover:shadow-purple-500/20"
            >
              <Palette className="w-6 h-6 text-gray-400 group-hover:text-purple-400 transition-colors duration-300" />
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </a>
            <a
              href="#"
              className="group relative p-4 rounded-full bg-white/5 border border-white/10 hover:border-cyan-400/50 transition-all duration-300 transform hover:scale-110 hover:shadow-lg hover:shadow-cyan-500/20"
            >
              <Mail className="w-6 h-6 text-gray-400 group-hover:text-cyan-400 transition-colors duration-300" />
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-cyan-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </a>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 py-8 px-4 border-t border-white/10">
        <div className="max-w-6xl mx-auto text-center">
          <p className="text-gray-400 text-sm">© 2024 Manasvi. Designed with passion and precision.</p>
        </div>
      </footer>

      {/* Modal for viewing full-size content */}
      {selectedProject && (
        <div
          className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedProject(null)}
        >
          <div
            className="relative max-w-4xl max-h-[90vh] w-full"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={() => setSelectedProject(null)}
              className="absolute -top-12 right-0 text-white hover:text-cyan-400 transition-colors duration-300"
            >
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <div className="bg-white/10 backdrop-blur-md rounded-2xl overflow-hidden">
              {selectedProject.isVideo ? (
                <video
                  src={selectedProject.media}
                  className="w-full max-h-[70vh] object-contain"
                  controls
                  autoPlay
                  muted
                  loop
                />
              ) : (
                <Image
                  src={selectedProject.media}
                  alt={selectedProject.title}
                  width={800}
                  height={600}
                  className="w-full max-h-[70vh] object-contain"
                />
              )}

              <div className="p-6">
                <h3 className="text-2xl font-bold text-white mb-2">{selectedProject.title}</h3>
                <p className="text-gray-300 mb-4">{selectedProject.description}</p>
                <span className="inline-block bg-cyan-500/20 text-cyan-400 px-3 py-1 rounded-full text-sm">
                  {selectedProject.type}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
