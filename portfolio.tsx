"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Mail,
  Linkedin,
  Palette,
  Download,
  Send,
  ExternalLink,
  Play,
  ImageIcon,
  FileText,
  Brush,
  Layout,
  Zap,
} from "lucide-react"
import Image from "next/image"

export default function Portfolio() {
  const [activeTab, setActiveTab] = useState("videos")
  const [hoveredProject, setHoveredProject] = useState<number | null>(null)

  const tabs = [
    { id: "videos", label: "Video Editing", icon: Play, count: "12+" },
    { id: "logos", label: "Logo Design", icon: Zap, count: "25+" },
    { id: "posters", label: "Poster Design", icon: ImageIcon, count: "18+" },
    { id: "drawing", label: "Digital Art", icon: Brush, count: "30+" },
    { id: "layouts", label: "Layout Design", icon: Layout, count: "15+" },
    { id: "motion", label: "Motion Graphics", icon: FileText, count: "8+" },
  ]

  // Placeholder content for each category - you can replace these with your actual work
  const portfolioContent = {
    videos: [
      {
        id: 1,
        title: "Corporate Promo Video",
        description: "Professional corporate video with motion graphics and color grading.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Video",
      },
      {
        id: 2,
        title: "Music Video Edit",
        description: "Creative music video with dynamic cuts and visual effects.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Video",
      },
      {
        id: 3,
        title: "Event Highlight Reel",
        description: "Fast-paced event coverage with synchronized audio.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Video",
      },
      {
        id: 4,
        title: "Product Demo Video",
        description: "Clean product showcase with smooth transitions.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Video",
      },
      {
        id: 5,
        title: "Social Media Content",
        description: "Short-form content optimized for social platforms.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Video",
      },
      {
        id: 6,
        title: "Documentary Edit",
        description: "Long-form documentary with narrative structure.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Video",
      },
    ],
    logos: [
      {
        id: 1,
        title: "Tech Startup Logo",
        description: "Modern minimalist logo for technology company.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Logo",
      },
      {
        id: 2,
        title: "Restaurant Brand Identity",
        description: "Warm and inviting logo design for local restaurant.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Logo",
      },
      {
        id: 3,
        title: "Fitness Brand Logo",
        description: "Dynamic logo representing strength and movement.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Logo",
      },
      {
        id: 4,
        title: "Creative Agency Logo",
        description: "Artistic logo showcasing creativity and innovation.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Logo",
      },
      {
        id: 5,
        title: "E-commerce Brand",
        description: "Clean and trustworthy logo for online store.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Logo",
      },
      {
        id: 6,
        title: "Non-profit Organization",
        description: "Meaningful logo representing community values.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Logo",
      },
    ],
    posters: [
      {
        id: 1,
        title: "Music Festival Poster",
        description: "Vibrant poster design for summer music festival.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Poster",
      },
      {
        id: 2,
        title: "Movie Poster Design",
        description: "Dramatic poster for independent film project.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Poster",
      },
      {
        id: 3,
        title: "Event Promotion",
        description: "Eye-catching design for corporate event.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Poster",
      },
      {
        id: 4,
        title: "Product Launch Poster",
        description: "Modern poster for new product announcement.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Poster",
      },
      {
        id: 5,
        title: "Concert Poster",
        description: "Artistic poster for live music performance.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Poster",
      },
      {
        id: 6,
        title: "Workshop Flyer",
        description: "Informative design for educational workshop.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Poster",
      },
    ],
    drawing: [
      {
        id: 1,
        title: "Character Illustration",
        description: "Digital character design with detailed shading.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Digital Art",
      },
      {
        id: 2,
        title: "Landscape Artwork",
        description: "Scenic digital painting with atmospheric lighting.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Digital Art",
      },
      {
        id: 3,
        title: "Abstract Composition",
        description: "Modern abstract art with bold colors.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Digital Art",
      },
      {
        id: 4,
        title: "Portrait Study",
        description: "Realistic digital portrait with fine details.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Digital Art",
      },
      {
        id: 5,
        title: "Concept Art",
        description: "Fantasy concept art for game development.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Digital Art",
      },
      {
        id: 6,
        title: "Illustration Series",
        description: "Cohesive illustration set for storytelling.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Digital Art",
      },
    ],
    layouts: [
      {
        id: 1,
        title: "Magazine Layout",
        description: "Editorial design with perfect typography balance.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Layout",
      },
      {
        id: 2,
        title: "Brochure Design",
        description: "Tri-fold brochure with compelling visual hierarchy.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Layout",
      },
      {
        id: 3,
        title: "Annual Report",
        description: "Professional report design with data visualization.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Layout",
      },
      {
        id: 4,
        title: "Book Cover Design",
        description: "Captivating book cover with artistic elements.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Layout",
      },
      {
        id: 5,
        title: "Website Layout",
        description: "Modern web design with user-friendly interface.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Layout",
      },
      {
        id: 6,
        title: "Presentation Template",
        description: "Professional slide deck with consistent branding.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Layout",
      },
    ],
    motion: [
      {
        id: 1,
        title: "Logo Animation",
        description: "Smooth logo reveal with particle effects.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Motion Graphics",
      },
      {
        id: 2,
        title: "Explainer Animation",
        description: "Educational animation with clear visual storytelling.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Motion Graphics",
      },
      {
        id: 3,
        title: "Social Media Animation",
        description: "Engaging animated content for social platforms.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Motion Graphics",
      },
      {
        id: 4,
        title: "Title Sequence",
        description: "Cinematic title sequence with dynamic typography.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Motion Graphics",
      },
      {
        id: 5,
        title: "Infographic Animation",
        description: "Data visualization with smooth transitions.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Motion Graphics",
      },
      {
        id: 6,
        title: "Brand Animation",
        description: "Comprehensive brand animation package.",
        image: "/placeholder.svg?height=300&width=400",
        type: "Motion Graphics",
      },
    ],
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white overflow-x-hidden">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-3/4 left-1/2 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Header */}
      <header className="relative z-10 w-full py-8 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-cyan-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent mb-2">
            Manasvi
          </h1>
          <div className="relative inline-block">
            <p className="text-xl md:text-2xl text-gray-300 font-light tracking-wide">
              Video Editor & Graphic Designer
            </p>
            <div className="absolute -bottom-2 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-cyan-400 to-transparent animate-pulse"></div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative z-10 py-20 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <p className="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed">
            Creating stunning visuals and compelling video content that tells your story.
          </p>
          <Button className="group relative overflow-hidden bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-400 hover:to-purple-400 text-white font-semibold py-3 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-cyan-500/25">
            <span className="relative z-10 flex items-center gap-2">
              <Download className="w-5 h-5" />
              View My Reel
            </span>
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-cyan-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </Button>
        </div>
      </section>

      {/* Portfolio Section with Tabs */}
      <section className="relative z-10 py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold text-center mb-16 bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
            My Work
          </h2>

          {/* Tab Navigation */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {tabs.map((tab) => {
              const IconComponent = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`group relative flex items-center gap-2 px-6 py-3 rounded-full transition-all duration-300 transform hover:scale-105 ${
                    activeTab === tab.id
                      ? "bg-gradient-to-r from-cyan-500 to-purple-500 text-white shadow-lg shadow-cyan-500/25"
                      : "bg-white/5 border border-white/10 text-gray-300 hover:border-cyan-400/50 hover:text-cyan-400"
                  }`}
                >
                  <IconComponent className="w-4 h-4" />
                  <span className="font-medium">{tab.label}</span>
                  <span className="text-xs opacity-75">({tab.count})</span>
                  {activeTab !== tab.id && (
                    <div className="absolute inset-0 rounded-full bg-gradient-to-r from-cyan-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  )}
                </button>
              )
            })}
          </div>

          {/* Tab Content */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {portfolioContent[activeTab as keyof typeof portfolioContent]?.map((project) => (
              <div
                key={project.id}
                className="group relative overflow-hidden rounded-2xl backdrop-blur-md bg-white/5 border border-white/10 hover:border-cyan-400/50 transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-cyan-500/20"
                onMouseEnter={() => setHoveredProject(project.id)}
                onMouseLeave={() => setHoveredProject(null)}
              >
                <div className="relative overflow-hidden">
                  <Image
                    src={project.image || "/placeholder.svg"}
                    alt={project.title}
                    width={400}
                    height={300}
                    className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-2 text-white group-hover:text-cyan-400 transition-colors duration-300">
                    {project.title}
                  </h3>
                  <p className="text-gray-400 text-sm leading-relaxed">{project.description}</p>
                </div>
                {hoveredProject === project.id && (
                  <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-cyan-500/20 backdrop-blur-sm rounded-full p-2 border border-cyan-400/30">
                      <ExternalLink className="w-4 h-4 text-cyan-400" />
                    </div>
                  </div>
                )}
                <div className="absolute bottom-4 left-4 bg-black/50 backdrop-blur-sm text-white text-xs py-1 px-2 rounded-full">
                  {project.type}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="relative z-10 py-20 px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold text-center mb-16 bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
            Let's Connect
          </h2>

          {/* Contact Form */}
          <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-8 mb-12">
            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300">Name</label>
                  <Input
                    className="bg-white/5 border-white/20 text-white placeholder:text-gray-400 focus:border-cyan-400 focus:ring-cyan-400/20 rounded-lg"
                    placeholder="Your name"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300">Email</label>
                  <Input
                    type="email"
                    className="bg-white/5 border-white/20 text-white placeholder:text-gray-400 focus:border-cyan-400 focus:ring-cyan-400/20 rounded-lg"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Message</label>
                <Textarea
                  className="bg-white/5 border-white/20 text-white placeholder:text-gray-400 focus:border-cyan-400 focus:ring-cyan-400/20 rounded-lg min-h-[120px]"
                  placeholder="Tell me about your video editing or graphic design project..."
                />
              </div>
              <Button className="w-full group relative overflow-hidden bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-400 hover:to-purple-400 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-cyan-500/25">
                <span className="relative z-10 flex items-center justify-center gap-2">
                  <Send className="w-5 h-5" />
                  Send Message
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-cyan-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </Button>
            </form>
          </div>

          {/* Social Links */}
          <div className="flex justify-center space-x-8">
            <a
              href="#"
              className="group relative p-4 rounded-full bg-white/5 border border-white/10 hover:border-cyan-400/50 transition-all duration-300 transform hover:scale-110 hover:shadow-lg hover:shadow-cyan-500/20"
            >
              <Linkedin className="w-6 h-6 text-gray-400 group-hover:text-cyan-400 transition-colors duration-300" />
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-cyan-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </a>
            <a
              href="#"
              className="group relative p-4 rounded-full bg-white/5 border border-white/10 hover:border-purple-400/50 transition-all duration-300 transform hover:scale-110 hover:shadow-lg hover:shadow-purple-500/20"
            >
              <Palette className="w-6 h-6 text-gray-400 group-hover:text-purple-400 transition-colors duration-300" />
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </a>
            <a
              href="#"
              className="group relative p-4 rounded-full bg-white/5 border border-white/10 hover:border-cyan-400/50 transition-all duration-300 transform hover:scale-110 hover:shadow-lg hover:shadow-cyan-500/20"
            >
              <Mail className="w-6 h-6 text-gray-400 group-hover:text-cyan-400 transition-colors duration-300" />
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-cyan-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </a>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 py-8 px-4 border-t border-white/10">
        <div className="max-w-6xl mx-auto text-center">
          <p className="text-gray-400 text-sm">© 2024 Manasvi. Designed with passion and precision.</p>
        </div>
      </footer>
    </div>
  )
}
